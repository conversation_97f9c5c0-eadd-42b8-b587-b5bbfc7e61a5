import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../employees/data/repositories/employees_repository.dart';
import '../../data/repositories/time_slots_repository.dart';
import '../bloc/time_slots_bloc.dart';
import '../bloc/time_slots_event.dart';
import '../bloc/time_slots_state.dart';
import 'time_slot_form_page.dart';

class ProfessionalSchedulesPage extends StatefulWidget {
  const ProfessionalSchedulesPage({super.key});

  @override
  State<ProfessionalSchedulesPage> createState() => _ProfessionalSchedulesPageState();
}

class _ProfessionalSchedulesPageState extends State<ProfessionalSchedulesPage>
    with TickerProviderStateMixin {
  List<EmployeeModel> _employees = [];
  EmployeeModel? _selectedEmployee;
  List<TimeSlotModel> _employeeTimeSlots = [];
  bool _isLoadingEmployees = false;
  bool _isLoadingTimeSlots = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _dayNames = [
    'الأحد',    // 0
    'الإثنين',  // 1
    'الثلاثاء', // 2
    'الأربعاء', // 3
    'الخميس',   // 4
    'الجمعة',   // 5
    'السبت',    // 6
  ];

  final List<IconData> _dayIcons = [
    Icons.wb_sunny,      // الأحد
    Icons.work,          // الإثنين
    Icons.business,      // الثلاثاء
    Icons.school,        // الأربعاء
    Icons.local_hospital,// الخميس
    Icons.mosque,        // الجمعة
    Icons.weekend,       // السبت
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadEmployees();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoadingEmployees = true;
    });
    
    try {
      final employees = await EmployeesRepository.getAllEmployees();
      final specialists = employees.where((emp) => 
        emp.employeeType == 'specialist' && emp.isActive
      ).toList();
      
      setState(() {
        _employees = specialists;
        _isLoadingEmployees = false;
      });
      
      // Auto-select first employee if available
      if (specialists.isNotEmpty && _selectedEmployee == null) {
        _selectEmployee(specialists.first);
      }
    } catch (e) {
      setState(() {
        _isLoadingEmployees = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل الأخصائيين: $e')),
        );
      }
    }
  }

  Future<void> _loadEmployeeTimeSlots(String employeeId) async {
    setState(() {
      _isLoadingTimeSlots = true;
    });
    
    try {
      final timeSlots = await TimeSlotsRepository().getEmployeeTimeSlots(employeeId);
      setState(() {
        _employeeTimeSlots = timeSlots;
        _isLoadingTimeSlots = false;
      });
      _animationController.forward();
    } catch (e) {
      setState(() {
        _isLoadingTimeSlots = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل مواعيد الأخصائي: $e')),
        );
      }
    }
  }

  void _selectEmployee(EmployeeModel employee) {
    setState(() {
      _selectedEmployee = employee;
    });
    _animationController.reset();
    _loadEmployeeTimeSlots(employee.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header with gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topRight,
                end: Alignment.bottomLeft,
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.schedule,
                            color: Colors.white,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'إدارة مواعيد الأخصائيين',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                'تنظيم وإدارة جداول العمل',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: _selectedEmployee != null ? _showAddTimeSlotDialog : null,
                            icon: const Icon(Icons.add, color: Colors.white),
                            tooltip: 'إضافة موعد جديد',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    _buildEmployeeSelector(),
                  ],
                ),
              ),
            ),
          ),
          
          // Content
          Expanded(
            child: _selectedEmployee == null 
                ? _buildEmptyState()
                : _buildEmployeeScheduleView(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeSelector() {
    if (_isLoadingEmployees) {
      return Container(
        height: 80,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    if (_employees.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Row(
          children: [
            Icon(Icons.info, color: Colors.white70),
            SizedBox(width: 12),
            Text(
              'لا يوجد أخصائيين نشطين',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: 75,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        itemCount: _employees.length,
        itemBuilder: (context, index) {
          final employee = _employees[index];
          final isSelected = _selectedEmployee?.id == employee.id;

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: _buildEmployeeCard(employee, isSelected),
          );
        },
      ),
    );
  }

  Widget _buildEmployeeCard(EmployeeModel employee, bool isSelected) {
    final color = employee.specialization?.color != null 
        ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
        : AppColors.primary;

    return GestureDetector(
      onTap: () => _selectEmployee(employee),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 110,
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? color : Colors.white.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 12,
              backgroundColor: color,
              child: Text(
                employee.name.isNotEmpty ? employee.name[0] : 'أ',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 3),
            Flexible(
              child: Text(
                employee.name,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: isSelected ? AppColors.textPrimary : Colors.white,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
            ),
            if (employee.specialization != null) ...[
              const SizedBox(height: 1),
              Flexible(
                child: Text(
                  employee.specialization!.name,
                  style: TextStyle(
                    fontSize: 7,
                    color: isSelected ? AppColors.textSecondary : Colors.white70,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person_search,
              size: 64,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'اختر أخصائي لعرض جدوله',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'يمكنك إدارة مواعيد كل أخصائي على حدة',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeScheduleView() {
    if (_selectedEmployee == null) return const SizedBox();

    return Column(
      children: [
        // Employee info header
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: _selectedEmployee!.specialization?.color != null
                    ? Color(int.parse(_selectedEmployee!.specialization!.color.replaceFirst('#', '0xFF')))
                    : AppColors.primary,
                child: Text(
                  _selectedEmployee!.name.isNotEmpty ? _selectedEmployee!.name[0] : 'أ',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedEmployee!.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    if (_selectedEmployee!.specialization != null) ...[
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Color(int.parse(_selectedEmployee!.specialization!.color.replaceFirst('#', '0xFF'))).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _selectedEmployee!.specialization!.name,
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(int.parse(_selectedEmployee!.specialization!.color.replaceFirst('#', '0xFF'))),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.schedule, size: 16, color: AppColors.success),
                    const SizedBox(width: 4),
                    Text(
                      '${_employeeTimeSlots.length} موعد',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.success,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Schedule content
        Expanded(
          child: _isLoadingTimeSlots
              ? const Center(child: CircularProgressIndicator())
              : _employeeTimeSlots.isEmpty
                  ? _buildNoScheduleState()
                  : _buildScheduleList(),
        ),
      ],
    );
  }

  Widget _buildNoScheduleState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppColors.gray100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.event_busy,
              size: 64,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد مواعيد لـ ${_selectedEmployee!.name}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ابدأ بإضافة مواعيد لهذا الأخصائي',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddTimeSlotDialog,
            icon: const Icon(Icons.add),
            label: const Text('إضافة موعد جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleList() {
    // Group time slots by day
    final Map<int, List<TimeSlotModel>> groupedSlots = {};
    for (final slot in _employeeTimeSlots) {
      if (!groupedSlots.containsKey(slot.dayOfWeek)) {
        groupedSlots[slot.dayOfWeek] = [];
      }
      groupedSlots[slot.dayOfWeek]!.add(slot);
    }

    // Sort days
    final sortedDays = groupedSlots.keys.toList()..sort();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: RefreshIndicator(
        onRefresh: () async {
          if (_selectedEmployee != null) {
            await _loadEmployeeTimeSlots(_selectedEmployee!.id);
          }
        },
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: sortedDays.length,
          itemBuilder: (context, index) {
            final dayOfWeek = sortedDays[index];
            final daySlots = groupedSlots[dayOfWeek]!;

            // Sort slots by start time
            daySlots.sort((a, b) => a.startTime.compareTo(b.startTime));

            return _buildDayCard(dayOfWeek, daySlots);
          },
        ),
      ),
    );
  }

  Widget _buildDayCard(int dayOfWeek, List<TimeSlotModel> daySlots) {
    final color = _selectedEmployee!.specialization?.color != null
        ? Color(int.parse(_selectedEmployee!.specialization!.color.replaceFirst('#', '0xFF')))
        : AppColors.primary;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Day header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    dayOfWeek >= 0 && dayOfWeek < _dayIcons.length
                        ? _dayIcons[dayOfWeek]
                        : Icons.calendar_today,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _dayNames[dayOfWeek],
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${daySlots.length} موعد',
                    style: TextStyle(
                      fontSize: 12,
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Time slots
          ...daySlots.asMap().entries.map((entry) {
            final index = entry.key;
            final slot = entry.value;
            final isLast = index == daySlots.length - 1;

            return _buildTimeSlotItem(slot, isLast);
          }),

          // Add button
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: OutlinedButton.icon(
              onPressed: () => _showAddTimeSlotDialog(preSelectedDay: dayOfWeek),
              icon: const Icon(Icons.add, size: 18),
              label: Text('إضافة موعد في ${_dayNames[dayOfWeek]}'),
              style: OutlinedButton.styleFrom(
                foregroundColor: color,
                side: BorderSide(color: color),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSlotItem(TimeSlotModel slot, bool isLast) {
    final color = _selectedEmployee!.specialization?.color != null
        ? Color(int.parse(_selectedEmployee!.specialization!.color.replaceFirst('#', '0xFF')))
        : AppColors.primary;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: isLast ? null : Border(
          bottom: BorderSide(
            color: AppColors.gray200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Time indicator
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: slot.isActive ? color : AppColors.gray400,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 16),

          // Time info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  slot.timeRange,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: slot.isActive ? AppColors.textPrimary : AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Wrap(
                  spacing: 8,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.timer, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          '${slot.durationMinutes} دقيقة',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.people, size: 14, color: AppColors.textSecondary),
                        const SizedBox(width: 4),
                        Text(
                          'حد أقصى ${slot.maxPatients}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Status badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: slot.isActive ? AppColors.success : AppColors.gray400,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              slot.isActive ? 'نشط' : 'غير نشط',
              style: const TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // Actions menu
          PopupMenuButton<String>(
            onSelected: (value) => _handleTimeSlotAction(value, slot),
            icon: Icon(Icons.more_vert, color: AppColors.textSecondary),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, color: AppColors.primary, size: 20),
                    SizedBox(width: 8),
                    Text('تعديل'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'toggle',
                child: Row(
                  children: [
                    Icon(
                      slot.isActive ? Icons.pause : Icons.play_arrow,
                      color: slot.isActive ? Colors.orange : Colors.green,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(slot.isActive ? 'إيقاف' : 'تفعيل'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.content_copy, color: AppColors.info, size: 20),
                    SizedBox(width: 8),
                    Text('نسخ الموعد'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppColors.error, size: 20),
                    SizedBox(width: 8),
                    Text('حذف'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAddTimeSlotDialog({int? preSelectedDay}) {
    if (_selectedEmployee == null) return;

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<TimeSlotsBloc>(),
          child: TimeSlotFormPage(
            preSelectedDay: preSelectedDay,
            preSelectedEmployee: _selectedEmployee,
          ),
        ),
      ),
    ).then((_) {
      // Refresh data after adding
      if (_selectedEmployee != null) {
        _loadEmployeeTimeSlots(_selectedEmployee!.id);
      }
    });
  }

  void _handleTimeSlotAction(String action, TimeSlotModel timeSlot) {
    switch (action) {
      case 'edit':
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BlocProvider.value(
              value: context.read<TimeSlotsBloc>(),
              child: TimeSlotFormPage(
                timeSlot: timeSlot,
                isEditing: true,
              ),
            ),
          ),
        ).then((_) {
          // Refresh data after editing
          if (_selectedEmployee != null) {
            _loadEmployeeTimeSlots(_selectedEmployee!.id);
          }
        });
        break;
      case 'toggle':
        context.read<TimeSlotsBloc>().add(
          ToggleTimeSlotStatus(timeSlotId: timeSlot.id),
        );
        // Refresh immediately after toggle
        Future.delayed(const Duration(milliseconds: 300), () {
          if (_selectedEmployee != null && mounted) {
            _loadEmployeeTimeSlots(_selectedEmployee!.id);
          }
        });
        break;
      case 'duplicate':
        _duplicateTimeSlot(timeSlot);
        break;
      case 'delete':
        _showDeleteConfirmation(timeSlot);
        break;
    }
  }

  void _duplicateTimeSlot(TimeSlotModel originalSlot) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider.value(
          value: context.read<TimeSlotsBloc>(),
          child: TimeSlotFormPage(
            timeSlot: originalSlot.copyWith(
              id: '', // Clear ID for new slot
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
            isEditing: false, // This is a new slot, not editing
            preSelectedDay: originalSlot.dayOfWeek, // Keep the same day
            preSelectedEmployee: _selectedEmployee,
          ),
        ),
      ),
    ).then((_) {
      if (_selectedEmployee != null) {
        _loadEmployeeTimeSlots(_selectedEmployee!.id);
      }
    });
  }

  void _showDeleteConfirmation(TimeSlotModel timeSlot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Row(
          children: [
            Icon(Icons.warning, color: AppColors.error),
            SizedBox(width: 8),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل أنت متأكد من حذف الموعد "${timeSlot.timeRange}"؟'),
            const SizedBox(height: 8),
            const Text(
              'تحذير: سيتم حذف الموعد نهائياً ولن يمكن استرداده.',
              style: TextStyle(
                color: AppColors.error,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<TimeSlotsBloc>().add(
                DeleteTimeSlot(timeSlotId: timeSlot.id),
              );
              // Refresh after delete
              Future.delayed(const Duration(milliseconds: 500), () {
                if (_selectedEmployee != null) {
                  _loadEmployeeTimeSlots(_selectedEmployee!.id);
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
