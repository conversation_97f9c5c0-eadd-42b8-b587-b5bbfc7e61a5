import 'package:equatable/equatable.dart';
import 'patient_model.dart';
import 'invoice_item_model.dart';
import 'payment_model.dart';

enum PaymentType { cash, installment }
enum PaymentStatus { partial, paid, returned }
enum InvoiceStatus { active, cancelled, returned }

extension PaymentTypeExtension on PaymentType {
  String get value {
    switch (this) {
      case PaymentType.cash:
        return 'cash';
      case PaymentType.installment:
        return 'installment';
    }
  }

  String get arabicName {
    switch (this) {
      case PaymentType.cash:
        return 'كاش';
      case PaymentType.installment:
        return 'قسط';
    }
  }

  static PaymentType fromString(String value) {
    switch (value) {
      case 'cash':
        return PaymentType.cash;
      case 'installment':
        return PaymentType.installment;
      default:
        return PaymentType.cash;
    }
  }
}

extension PaymentStatusExtension on PaymentStatus {
  String get value {
    switch (this) {
      case PaymentStatus.partial:
        return 'partial';
      case PaymentStatus.paid:
        return 'paid';
      case PaymentStatus.returned:
        return 'returned';
    }
  }

  String get arabicName {
    switch (this) {
      case PaymentStatus.partial:
        return 'مدفوع جزئياً';
      case PaymentStatus.paid:
        return 'مدفوع بالكامل';
      case PaymentStatus.returned:
        return 'مرتجع';
    }
  }

  static PaymentStatus fromString(String value) {
    switch (value) {
      case 'partial':
        return PaymentStatus.partial;
      case 'paid':
        return PaymentStatus.paid;
      case 'returned':
        return PaymentStatus.returned;
      default:
        return PaymentStatus.partial;
    }
  }

  /// Calculate payment status based on invoice conditions
  static PaymentStatus calculateStatus({
    required PaymentType paymentType,
    required InvoiceStatus invoiceStatus,
    required double remainingAmount,
  }) {
    // If invoice is returned, status is returned
    if (invoiceStatus == InvoiceStatus.returned) {
      return PaymentStatus.returned;
    }

    // If cash payment or installment with no remaining amount, status is paid
    if (paymentType == PaymentType.cash || remainingAmount <= 0) {
      return PaymentStatus.paid;
    }

    // If installment with remaining amount, status is partial
    return PaymentStatus.partial;
  }
}

extension InvoiceStatusExtension on InvoiceStatus {
  String get value {
    switch (this) {
      case InvoiceStatus.active:
        return 'active';
      case InvoiceStatus.cancelled:
        return 'cancelled';
      case InvoiceStatus.returned:
        return 'returned';
    }
  }

  String get arabicName {
    switch (this) {
      case InvoiceStatus.active:
        return 'نشطة';
      case InvoiceStatus.cancelled:
        return 'ملغية';
      case InvoiceStatus.returned:
        return 'مسترجعة';
    }
  }

  static InvoiceStatus fromString(String value) {
    switch (value) {
      case 'active':
        return InvoiceStatus.active;
      case 'cancelled':
        return InvoiceStatus.cancelled;
      case 'returned':
        return InvoiceStatus.returned;
      default:
        return InvoiceStatus.active;
    }
  }
}

class SalesInvoiceModel extends Equatable {
  final String id;
  final String invoiceNumber;
  final String patientId;
  final PatientModel? patient;
  final double totalAmount;
  final double discountPercentage;
  final double discountAmount;
  final double finalAmount;
  final bool isCommissionSale;
  final double commissionPercentage;
  final double commissionAmount;
  final double returnAmount;
  final String? returnReason;
  final DateTime? returnedAt;
  final String? returnedBy;
  final PaymentType paymentType;
  final PaymentStatus paymentStatus;
  final double paidAmount;
  final double remainingAmount;
  final DateTime? dueDate;
  final String? notes;
  final InvoiceStatus status;
  final String? createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<InvoiceItemModel> items;
  final List<PaymentModel> payments;

  const SalesInvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.patientId,
    this.patient,
    required this.totalAmount,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    required this.finalAmount,
    this.isCommissionSale = false,
    this.commissionPercentage = 0.0,
    this.commissionAmount = 0.0,
    this.returnAmount = 0.0,
    this.returnReason,
    this.returnedAt,
    this.returnedBy,
    required this.paymentType,
    this.paymentStatus = PaymentStatus.partial,
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    this.dueDate,
    this.notes,
    this.status = InvoiceStatus.active,
    this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.items = const [],
    this.payments = const [],
  });

  factory SalesInvoiceModel.fromJson(Map<String, dynamic> json) {
    return SalesInvoiceModel(
      id: json['id'] as String? ?? '',
      invoiceNumber: json['invoice_number'] as String? ?? '',
      patientId: json['patient_id'] as String? ?? '',
      patient: json['patients'] != null
          ? PatientModel.fromJson(json['patients'] as Map<String, dynamic>)
          : null,
      totalAmount: (json['total_amount'] as num?)?.toDouble() ?? 0.0,
      discountPercentage: (json['discount_percentage'] as num?)?.toDouble() ?? 0.0,
      discountAmount: (json['discount_amount'] as num?)?.toDouble() ?? 0.0,
      finalAmount: (json['final_amount'] as num?)?.toDouble() ?? 0.0,
      isCommissionSale: json['is_commission_sale'] as bool? ?? false,
      commissionPercentage: (json['commission_percentage'] as num?)?.toDouble() ?? 0.0,
      commissionAmount: (json['commission_amount'] as num?)?.toDouble() ?? 0.0,
      returnAmount: (json['return_amount'] as num?)?.toDouble() ?? 0.0,
      returnReason: json['return_reason'] as String?,
      returnedAt: json['returned_at'] != null ? DateTime.parse(json['returned_at'] as String) : null,
      returnedBy: json['returned_by'] as String?,
      paymentType: PaymentTypeExtension.fromString(json['payment_type'] as String? ?? 'cash'),
      paymentStatus: PaymentStatusExtension.fromString(json['payment_status'] as String? ?? 'pending'),
      paidAmount: (json['paid_amount'] as num?)?.toDouble() ?? 0.0,
      remainingAmount: (json['remaining_amount'] as num?)?.toDouble() ?? 0.0,
      dueDate: json['due_date'] != null ? DateTime.parse(json['due_date'] as String) : null,
      notes: json['notes'] as String?,
      status: InvoiceStatusExtension.fromString(json['status'] as String? ?? 'active'),
      createdBy: json['created_by'] as String?,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at'] as String) : DateTime.now(),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at'] as String) : DateTime.now(),
      items: json['invoice_items'] != null
          ? (json['invoice_items'] as List)
              .map((item) => InvoiceItemModel.fromJson(item as Map<String, dynamic>))
              .toList()
          : [],
      payments: json['payments'] != null
          ? (json['payments'] as List)
              .map((payment) => PaymentModel.fromJson(payment as Map<String, dynamic>))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      // Don't include 'id' when creating new invoices, let database generate UUID
      'invoice_number': invoiceNumber,
      'patient_id': patientId,
      'total_amount': totalAmount,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'final_amount': finalAmount,
      'is_commission_sale': isCommissionSale,
      'commission_percentage': commissionPercentage,
      'commission_amount': commissionAmount,
      'return_amount': returnAmount,
      'return_reason': returnReason,
      'returned_at': returnedAt?.toIso8601String(),
      'returned_by': returnedBy,
      'payment_type': paymentType.value,
      'payment_status': paymentStatus.value,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'due_date': dueDate?.toIso8601String(),
      'notes': notes,
      'status': status.value,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SalesInvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? patientId,
    PatientModel? patient,
    double? totalAmount,
    double? discountPercentage,
    double? discountAmount,
    double? finalAmount,
    bool? isCommissionSale,
    double? commissionPercentage,
    double? commissionAmount,
    double? returnAmount,
    String? returnReason,
    DateTime? returnedAt,
    String? returnedBy,
    PaymentType? paymentType,
    PaymentStatus? paymentStatus,
    double? paidAmount,
    double? remainingAmount,
    DateTime? dueDate,
    String? notes,
    InvoiceStatus? status,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<InvoiceItemModel>? items,
    List<PaymentModel>? payments,
  }) {
    return SalesInvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      patientId: patientId ?? this.patientId,
      patient: patient ?? this.patient,
      totalAmount: totalAmount ?? this.totalAmount,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      isCommissionSale: isCommissionSale ?? this.isCommissionSale,
      commissionPercentage: commissionPercentage ?? this.commissionPercentage,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      returnAmount: returnAmount ?? this.returnAmount,
      returnReason: returnReason ?? this.returnReason,
      returnedAt: returnedAt ?? this.returnedAt,
      returnedBy: returnedBy ?? this.returnedBy,
      paymentType: paymentType ?? this.paymentType,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      dueDate: dueDate ?? this.dueDate,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      items: items ?? this.items,
      payments: payments ?? this.payments,
    );
  }

  @override
  List<Object?> get props => [
        id,
        invoiceNumber,
        patientId,
        patient,
        totalAmount,
        discountPercentage,
        discountAmount,
        finalAmount,
        isCommissionSale,
        commissionPercentage,
        commissionAmount,
        returnAmount,
        returnReason,
        returnedAt,
        returnedBy,
        paymentType,
        paymentStatus,
        paidAmount,
        remainingAmount,
        dueDate,
        notes,
        status,
        createdBy,
        createdAt,
        updatedAt,
        items,
        payments,
      ];
}
