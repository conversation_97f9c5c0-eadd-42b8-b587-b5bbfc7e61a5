import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

class AppointmentModel extends Equatable {
  final String id;
  final String? patientId;
  final String? employeeId;
  final DateTime appointmentDate;
  final String? appointmentTime;
  final String? timeSlotId; // Keep for backward compatibility
  final String status;
  final String appointmentType;
  final int durationMinutes;
  final String? sessionNotes;
  final DateTime? nextAppointmentDate;
  final String? notes;
  // New fields for enhanced booking system
  final double consultationFee; // سعر الكشف
  final double paidAmount; // المبلغ المدفوع
  final double remainingAmount; // المبلغ المتبقي
  final bool isMultipleBooking; // حجز متعدد أم لا
  final String? multipleBookingGroupId; // معرف مجموعة الحجوزات المتعددة
  final int? bookingSequence; // ترتيب الحجز في المجموعة
  final DateTime createdAt;
  final DateTime updatedAt;

  const AppointmentModel({
    required this.id,
    this.patientId,
    this.employeeId,
    required this.appointmentDate,
    this.appointmentTime,
    this.timeSlotId,
    this.status = 'available',
    this.appointmentType = 'consultation',
    this.durationMinutes = 30,
    this.sessionNotes,
    this.nextAppointmentDate,
    this.notes,
    // New fields with defaults
    this.consultationFee = 0.0,
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    this.isMultipleBooking = false,
    this.multipleBookingGroupId,
    this.bookingSequence,
    required this.createdAt,
    required this.updatedAt,
  });

  // Status constants
  static const String statusAvailable = 'available';
  static const String statusConfirmed = 'confirmed'; // حجز مؤكد
  static const String statusCompleted = 'completed'; // حجز مكتمل
  static const String statusCancelled = 'cancelled'; // حجز ملغي
  static const String statusNoShow = 'no_show'; // لم يحضر

  // Check if appointment is booked
  bool get isBooked =>
      status == 'booked' || status == statusConfirmed || status == 'scheduled';

  // Check if appointment is available
  bool get isAvailable => status == statusAvailable;

  // Check if appointment is completed
  bool get isCompleted => status == statusCompleted;

  // Check if appointment is cancelled
  bool get isCancelled => status == statusCancelled;

  // Check if patient didn't show up
  bool get isNoShow => status == statusNoShow;

  // Get status display name in Arabic
  String get statusDisplayName {
    switch (status) {
      case statusAvailable:
        return 'متاح';
      case statusConfirmed:
        return 'مؤكد';
      case statusCompleted:
        return 'مكتمل';
      case statusCancelled:
        return 'ملغي';
      case statusNoShow:
        return 'لم يحضر';
      default:
        return 'غير محدد';
    }
  }

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    try {
      debugPrint('🔄 AppointmentModel.fromJson: Parsing JSON: $json');

      final id = json['id'] as String;
      final patientId = json['patient_id'] as String?;
      final employeeId = json['employee_id'] as String?;
      final appointmentDate = DateTime.parse(
        json['appointment_date'] as String,
      );
      final appointmentTime = json['appointment_time'] as String?;
      final timeSlotId = json['time_slot_id'] as String?;
      final status = json['status'] as String? ?? 'available';
      final appointmentType = json['appointment_type'] as String? ?? 'consultation';
      final durationMinutes = json['duration_minutes'] as int? ?? 30;
      final sessionNotes = json['session_notes'] as String?;
      final nextAppointmentDate = json['next_appointment_date'] != null
          ? DateTime.parse(json['next_appointment_date'] as String)
          : null;
      final notes = json['notes'] as String?;
      // New fields
      final consultationFee = (json['consultation_fee'] as num?)?.toDouble() ?? 0.0;
      final paidAmount = (json['paid_amount'] as num?)?.toDouble() ?? 0.0;
      final remainingAmount = (json['remaining_amount'] as num?)?.toDouble() ?? 0.0;
      final isMultipleBooking = json['is_multiple_booking'] as bool? ?? false;
      final multipleBookingGroupId = json['multiple_booking_group_id'] as String?;
      final bookingSequence = json['booking_sequence'] as int?;
      final createdAt = DateTime.parse(json['created_at'] as String);
      final updatedAt = DateTime.parse(json['updated_at'] as String);

      debugPrint(
        '✅ AppointmentModel.fromJson: Successfully parsed appointment $id',
      );

      return AppointmentModel(
        id: id,
        patientId: patientId,
        employeeId: employeeId,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        timeSlotId: timeSlotId,
        status: status,
        appointmentType: appointmentType,
        durationMinutes: durationMinutes,
        sessionNotes: sessionNotes,
        nextAppointmentDate: nextAppointmentDate,
        notes: notes,
        consultationFee: consultationFee,
        paidAmount: paidAmount,
        remainingAmount: remainingAmount,
        isMultipleBooking: isMultipleBooking,
        multipleBookingGroupId: multipleBookingGroupId,
        bookingSequence: bookingSequence,
        createdAt: createdAt,
        updatedAt: updatedAt,
      );
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentModel.fromJson: Error parsing JSON: $e');
      debugPrint('📍 AppointmentModel.fromJson: JSON was: $json');
      debugPrint('📍 AppointmentModel.fromJson: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'patient_id': patientId,
      'employee_id': employeeId,
      'appointment_date': appointmentDate.toIso8601String().split('T')[0],
      'appointment_time': appointmentTime,
      'time_slot_id': timeSlotId,
      'status': status,
      'appointment_type': appointmentType,
      'duration_minutes': durationMinutes,
      'session_notes': sessionNotes,
      'next_appointment_date': nextAppointmentDate?.toIso8601String().split('T')[0],
      'notes': notes,
      // New fields
      'consultation_fee': consultationFee,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'is_multiple_booking': isMultipleBooking,
      'multiple_booking_group_id': multipleBookingGroupId,
      'booking_sequence': bookingSequence,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };

    // فقط أضف id إذا لم يكن فارغاً (للتحديث)
    if (id.isNotEmpty) {
      json['id'] = id;
    }

    debugPrint('🔄 AppointmentModel.toJson: Generated JSON: $json');
    return json;
  }

  AppointmentModel copyWith({
    String? id,
    String? patientId,
    String? employeeId,
    DateTime? appointmentDate,
    String? appointmentTime,
    String? timeSlotId,
    String? status,
    String? appointmentType,
    int? durationMinutes,
    String? sessionNotes,
    DateTime? nextAppointmentDate,
    String? notes,
    double? consultationFee,
    double? paidAmount,
    double? remainingAmount,
    bool? isMultipleBooking,
    String? multipleBookingGroupId,
    int? bookingSequence,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      employeeId: employeeId ?? this.employeeId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      timeSlotId: timeSlotId ?? this.timeSlotId,
      status: status ?? this.status,
      appointmentType: appointmentType ?? this.appointmentType,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      sessionNotes: sessionNotes ?? this.sessionNotes,
      nextAppointmentDate: nextAppointmentDate ?? this.nextAppointmentDate,
      notes: notes ?? this.notes,
      consultationFee: consultationFee ?? this.consultationFee,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      isMultipleBooking: isMultipleBooking ?? this.isMultipleBooking,
      multipleBookingGroupId: multipleBookingGroupId ?? this.multipleBookingGroupId,
      bookingSequence: bookingSequence ?? this.bookingSequence,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
    id,
    patientId,
    employeeId,
    appointmentDate,
    appointmentTime,
    timeSlotId,
    status,
    appointmentType,
    durationMinutes,
    sessionNotes,
    nextAppointmentDate,
    notes,
    consultationFee,
    paidAmount,
    remainingAmount,
    isMultipleBooking,
    multipleBookingGroupId,
    bookingSequence,
    createdAt,
    updatedAt,
  ];
}
