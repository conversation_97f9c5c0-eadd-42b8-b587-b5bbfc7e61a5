import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:developer' as developer;
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/treatment_type.dart';
import '../../../../core/models/patient_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AddPatientPage extends StatefulWidget {
  const AddPatientPage({super.key});

  @override
  State<AddPatientPage> createState() => _AddPatientPageState();
}

class _AddPatientPageState extends State<AddPatientPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  DateTime? _selectedBirthDate;
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  String _selectedGender = 'ذكر';
  List<TreatmentType> _selectedTreatmentTypes = [];
  bool _isPremium = false;
  bool _isLoading = false;
  
  final List<String> _genderOptions = ['ذكر', 'أنثى'];

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _toggleTreatmentType(TreatmentType type) {
    setState(() {
      if (_selectedTreatmentTypes.contains(type)) {
        _selectedTreatmentTypes.remove(type);
      } else {
        _selectedTreatmentTypes.add(type);
      }
    });
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }

  Future<bool?> _showSuccessDialog() async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false, // User must tap button
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Success Icon
              Container(
                width: 80.w,
                height: 80.h,
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 50.w,
                ),
              ),
              SizedBox(height: 24.h),

              // Success Title
              Text(
                'تم بنجاح!',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.success,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 12.h),

              // Success Message
              Text(
                'تم إنشاء حساب المريض بنجاح\nيمكنك الآن إدارة ملفه الطبي',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24.h),

              // OK Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (mounted) {
                      Navigator.of(context).pop(true);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                    padding: EdgeInsets.symmetric(vertical: 16.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                  ),
                  child: Text(
                    'حسناً',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _resetForm() {
    setState(() {
      _nameController.clear();
      _emailController.clear();
      _phoneController.clear();
      _passwordController.clear();
      _confirmPasswordController.clear();
      _selectedBirthDate = null;
      _selectedGender = 'ذكر';
      _selectedTreatmentTypes.clear();
      _isPremium = false;
    });
    developer.log('Form reset completed', name: 'AddPatientPage');
  }

  Future<void> _createPatient() async {
    developer.log('Starting patient creation process', name: 'AddPatientPage');

    if (!_formKey.currentState!.validate()) {
      developer.log('Form validation failed', name: 'AddPatientPage');
      return;
    }

    if (_selectedTreatmentTypes.isEmpty) {
      developer.log('No treatment types selected', name: 'AddPatientPage');
      _showErrorMessage('يرجى اختيار نوع العلاج على الأقل');
      return;
    }

    if (_selectedBirthDate == null) {
      developer.log('No birth date selected', name: 'AddPatientPage');
      _showErrorMessage('يرجى اختيار تاريخ الميلاد');
      return;
    }

    if (_passwordController.text != _confirmPasswordController.text) {
      developer.log('Password confirmation mismatch', name: 'AddPatientPage');
      _showErrorMessage('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
      return;
    }

    developer.log('All validations passed, starting creation', name: 'AddPatientPage');
    setState(() => _isLoading = true);

    try {
      // Create user in Supabase Auth
      final AuthResponse authResponse = await Supabase.instance.client.auth.signUp(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        data: {
          'name': _nameController.text.trim(),
          'user_type': 'patient',
        },
      );

      if (authResponse.user == null) {
        throw Exception('فشل في إنشاء حساب المريض');
      }

      developer.log('Auth user created successfully: ${authResponse.user!.id}', name: 'AddPatientPage');

      // Calculate age from birth date
      int? calculatedAge;
      if (_selectedBirthDate != null) {
        final now = DateTime.now();
        calculatedAge = now.year - _selectedBirthDate!.year;
        if (now.month < _selectedBirthDate!.month ||
            (now.month == _selectedBirthDate!.month && now.day < _selectedBirthDate!.day)) {
          calculatedAge--;
        }
        developer.log('Age calculated: $calculatedAge years', name: 'AddPatientPage');
      }

      // Convert gender to English for database
      String genderForDB = _selectedGender == 'ذكر' ? 'male' : 'female';
      developer.log('Gender converted: $_selectedGender -> $genderForDB', name: 'AddPatientPage');

      // Create patient record in patients table
      final patient = PatientModel(
        id: authResponse.user!.id, // Use auth.uid as patient ID
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        age: calculatedAge,
        birthDate: _selectedBirthDate,
        gender: genderForDB,
        treatmentTypes: _selectedTreatmentTypes,
        isPremium: _isPremium,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      developer.log('Inserting patient data into database', name: 'AddPatientPage');
      await Supabase.instance.client
          .from('patients')
          .insert(patient.toJson());

      developer.log('Patient data inserted successfully', name: 'AddPatientPage');

      if (mounted) {
        // Clear all form data first
        _resetForm();

        developer.log('Patient creation completed successfully', name: 'AddPatientPage');

        // Show success dialog and wait for user to dismiss it
        final dialogResult = await _showSuccessDialog();

        // Only navigate if dialog was properly dismissed and widget is still mounted
        if (mounted && dialogResult == true) {
          // Add small delay to ensure UI is ready
          await Future.delayed(const Duration(milliseconds: 100));
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        }
      }
    } catch (e) {
      developer.log('Error creating patient account: $e', name: 'AddPatientPage');
      if (mounted) {
        String errorMessage = 'خطأ في إنشاء حساب المريض';

        // Handle specific error types
        if (e.toString().contains('patients_gender_check')) {
          errorMessage = 'خطأ في قيمة الجنس. يرجى المحاولة مرة أخرى';
        } else if (e.toString().contains('duplicate key')) {
          errorMessage = 'البريد الإلكتروني مستخدم بالفعل';
        } else if (e.toString().contains('invalid email')) {
          errorMessage = 'البريد الإلكتروني غير صحيح';
        } else if (e.toString().contains('weak password')) {
          errorMessage = 'كلمة المرور ضعيفة. يجب أن تكون 6 أحرف على الأقل';
        } else if (e.toString().contains('network')) {
          errorMessage = 'خطأ في الاتصال. يرجى التحقق من الإنترنت';
        }

        _showErrorMessage(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إضافة مريض جديد'),
        backgroundColor: AppColors.white,
        elevation: 0,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header info
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                  border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                ),
                child: Column(
                  children: [
                    Icon(Icons.person_add, color: AppColors.primary, size: 32),
                    SizedBox(height: 8.h),
                    Text(
                      'إنشاء حساب مريض جديد',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'سيتم إنشاء حساب للمريض في النظام مع إمكانية تسجيل الدخول',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              SizedBox(height: 24.h),
              
              // Basic Information
              _buildSectionTitle('المعلومات الأساسية'),
              SizedBox(height: 16.h),
              
              _buildTextField(
                controller: _nameController,
                label: 'الاسم الكامل *',
                icon: Icons.person,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال الاسم الكامل';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              _buildTextField(
                controller: _emailController,
                label: 'البريد الإلكتروني *',
                icon: Icons.email,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال البريد الإلكتروني';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                    return 'يرجى إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              _buildTextField(
                controller: _phoneController,
                label: 'رقم الهاتف',
                icon: Icons.phone,
                keyboardType: TextInputType.phone,
              ),
              
              SizedBox(height: 16.h),
              
              Row(
                children: [
                  Expanded(
                    child: _buildBirthDateField(),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: _buildGenderDropdown(),
                  ),
                ],
              ),
              
              SizedBox(height: 24.h),
              
              // Authentication Information
              _buildSectionTitle('معلومات تسجيل الدخول'),
              SizedBox(height: 16.h),
              
              _buildTextField(
                controller: _passwordController,
                label: 'كلمة المرور *',
                icon: Icons.lock,
                obscureText: true,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value!.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 16.h),
              
              _buildTextField(
                controller: _confirmPasswordController,
                label: 'تأكيد كلمة المرور *',
                icon: Icons.lock_outline,
                obscureText: true,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'يرجى تأكيد كلمة المرور';
                  }
                  return null;
                },
              ),
              
              SizedBox(height: 24.h),
              
              // Treatment Types
              _buildSectionTitle('نوع العلاج المطلوب *'),
              SizedBox(height: 16.h),
              
              _buildTreatmentTypesSelection(),
              
              SizedBox(height: 24.h),
              
              // Premium Status
              _buildPremiumSwitch(),

              SizedBox(height: 40.h),

              // Create Account Button
              SizedBox(
                width: double.infinity,
                height: 50.h,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _createPatient,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    elevation: 2,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                          ),
                        )
                      : Text(
                          'إنشاء حساب المريض',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),

              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBirthDateField() {
    return InkWell(
      onTap: () async {
        final DateTime? picked = await showDatePicker(
          context: context,
          initialDate: _selectedBirthDate ?? DateTime.now().subtract(const Duration(days: 365 * 25)),
          firstDate: DateTime(1900),
          lastDate: DateTime.now(),
          locale: const Locale('ar'),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: AppColors.primary,
                  onPrimary: AppColors.white,
                  surface: AppColors.white,
                  onSurface: AppColors.textPrimary,
                ),
              ),
              child: child!,
            );
          },
        );
        if (picked != null && picked != _selectedBirthDate) {
          setState(() {
            _selectedBirthDate = picked;
          });
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.gray300),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            const Icon(Icons.calendar_today, color: AppColors.primary),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تاريخ الميلاد',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _selectedBirthDate != null
                        ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                        : 'اختر تاريخ الميلاد',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: _selectedBirthDate != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool obscureText = false,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
      ),
    );
  }

  Widget _buildGenderDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedGender,
      decoration: InputDecoration(
        labelText: 'الجنس',
        prefixIcon: const Icon(Icons.person_outline, color: AppColors.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
      ),
      items: _genderOptions.map((gender) {
        return DropdownMenuItem<String>(
          value: gender,
          child: Text(gender),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedGender = value;
          });
        }
      },
    );
  }

  Widget _buildTreatmentTypesSelection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.gray300),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر نوع أو أكثر من أنواع العلاج:',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          SizedBox(height: 12.h),
          TreatmentTypeHelper.buildSelectionGrid(
            _selectedTreatmentTypes,
            _toggleTreatmentType,
          ),
          if (_selectedTreatmentTypes.isEmpty)
            Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                'يرجى اختيار نوع العلاج على الأقل',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.error,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPremiumSwitch() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.gray50,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(Icons.star, color: AppColors.warning, size: 24),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'عضوية مميزة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'يحصل على خدمات إضافية ومميزة',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isPremium,
            onChanged: (value) {
              setState(() {
                _isPremium = value;
              });
            },
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }
}
