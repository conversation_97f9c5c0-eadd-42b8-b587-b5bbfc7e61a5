import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/time_slot_model.dart';
import '../../../appointments/data/repositories/appointments_repository.dart';
import '../../../appointments/data/repositories/time_slots_repository.dart';
import '../../../appointments/data/repositories/holidays_repository.dart';
import '../../../patients/data/repositories/patients_repository.dart';
import 'print_appointment_invoice_page.dart';

class EditAppointmentPage extends StatefulWidget {
  final AppointmentModel appointment;

  const EditAppointmentPage({
    super.key,
    required this.appointment,
  });

  @override
  State<EditAppointmentPage> createState() => _EditAppointmentPageState();
}

class _EditAppointmentPageState extends State<EditAppointmentPage> {
  final AppointmentsRepository _appointmentsRepository = AppointmentsRepository();
  final TimeSlotsRepository _timeSlotsRepository = TimeSlotsRepository();
  final HolidaysRepository _holidaysRepository = HolidaysRepository();
  final PatientsRepository _patientsRepository = PatientsRepository();

  late TextEditingController _consultationFeeController;
  late TextEditingController _paidAmountController;
  late TextEditingController _notesController;

  DateTime? _selectedDate;
  TimeSlotModel? _selectedTimeSlot;
  List<TimeSlotModel> _availableTimeSlots = [];
  bool _isLoadingTimeSlots = false;
  String _selectedStatus = AppointmentModel.statusConfirmed;
  PatientModel? _patient;
  bool _isLoadingPatient = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadPatientData();
  }

  void _initializeControllers() {
    _consultationFeeController = TextEditingController(
      text: widget.appointment.consultationFee.toString(),
    );
    _paidAmountController = TextEditingController(
      text: widget.appointment.paidAmount.toString(),
    );
    _notesController = TextEditingController(
      text: widget.appointment.notes ?? '',
    );
    _selectedDate = widget.appointment.appointmentDate;
    _selectedStatus = widget.appointment.status;
  }

  Future<void> _loadPatientData() async {
    if (widget.appointment.patientId == null) return;

    setState(() {
      _isLoadingPatient = true;
    });

    try {
      final patient = await _patientsRepository.getPatientById(widget.appointment.patientId!);
      setState(() {
        _patient = patient;
        _isLoadingPatient = false;
      });
      
      // Load time slots for the current date
      await _loadAvailableTimeSlots();
    } catch (e) {
      setState(() {
        _isLoadingPatient = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات المريض: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      // Check if the selected date is a holiday
      final isHoliday = await _checkIfHoliday(picked);
      if (isHoliday) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('التاريخ المحدد يوافق إجازة، يرجى اختيار تاريخ آخر'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      setState(() {
        _selectedDate = picked;
        _selectedTimeSlot = null;
        _availableTimeSlots = [];
      });
      
      await _loadAvailableTimeSlots();
    }
  }

  Future<bool> _checkIfHoliday(DateTime date) async {
    try {
      final holidays = await _holidaysRepository.getHolidaysByDate(date);
      return holidays.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking holidays: $e');
      return false;
    }
  }

  Future<void> _loadAvailableTimeSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoadingTimeSlots = true;
    });

    try {
      final timeSlots = await _timeSlotsRepository.getAvailableTimeSlots(_selectedDate!);
      
      // Add current time slot to available slots if it's not already there
      if (widget.appointment.timeSlotId != null) {
        final currentTimeSlot = await _timeSlotsRepository.getTimeSlotById(widget.appointment.timeSlotId!);
        if (currentTimeSlot != null && !timeSlots.any((slot) => slot.id == currentTimeSlot.id)) {
          timeSlots.add(currentTimeSlot);
          timeSlots.sort((a, b) => a.startTime.compareTo(b.startTime));
        }
        _selectedTimeSlot = currentTimeSlot;
      }
      
      setState(() {
        _availableTimeSlots = timeSlots;
        _isLoadingTimeSlots = false;
      });
    } catch (e) {
      setState(() {
        _availableTimeSlots = [];
        _isLoadingTimeSlots = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الأوقات المتاحة: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _updateAppointment() async {
    if (_selectedDate == null || _selectedTimeSlot == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى اختيار التاريخ والوقت'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final consultationFee = double.tryParse(_consultationFeeController.text) ?? 0.0;
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0.0;

    try {
      final updatedAppointment = widget.appointment.copyWith(
        appointmentDate: _selectedDate,
        timeSlotId: _selectedTimeSlot!.id,
        status: _selectedStatus,
        consultationFee: consultationFee,
        paidAmount: paidAmount,
        remainingAmount: consultationFee - paidAmount,
        notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
        updatedAt: DateTime.now(),
      );

      await _appointmentsRepository.updateAppointment(updatedAppointment);

      if (mounted) {
        _showSuccessDialog(updatedAppointment);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الموعد: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _showSuccessDialog(AppointmentModel appointment) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: AppColors.success, size: 24.sp),
            SizedBox(width: 8.w),
            Text(
              'تم التحديث بنجاح',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
          ],
        ),
        content: Text(
          'تم تحديث الموعد بنجاح. هل تريد طباعة فاتورة الحجز المحدثة؟',
          style: TextStyle(fontSize: 14.sp),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to appointments
            },
            child: Text(
              'لا، شكراً',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => PrintAppointmentInvoicePage(
                    appointment: appointment,
                    patient: _patient,
                  ),
                ),
              ).then((_) {
                if (mounted) {
                  Navigator.of(context).pop(); // Go back to appointments after printing
                }
              });
            },
            icon: Icon(Icons.print, size: 16.sp, color: AppColors.white),
            label: Text(
              'طباعة الفاتورة',
              style: TextStyle(color: AppColors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _consultationFeeController.dispose();
    _paidAmountController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تعديل الموعد',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        elevation: 0,
        iconTheme: IconThemeData(color: AppColors.white),
        actions: [
          TextButton(
            onPressed: _updateAppointment,
            child: Text(
              'حفظ',
              style: TextStyle(
                color: AppColors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: _isLoadingPatient
          ? Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Patient Info Card
                  if (_patient != null) ...[
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12.r),
                        border: Border.all(color: AppColors.primary),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'بيانات المريض',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                          SizedBox(height: 8.h),
                          Text(
                            'الاسم: ${_patient!.name}',
                            style: TextStyle(fontSize: 14.sp),
                          ),
                          Text(
                            'رقم المريض: ${_patient!.id}',
                            style: TextStyle(fontSize: 14.sp),
                          ),
                          if (_patient!.phone != null)
                            Text(
                              'الهاتف: ${_patient!.phone}',
                              style: TextStyle(fontSize: 14.sp),
                            ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.h),
                  ],

                  // Status Selection
                  _buildSectionTitle('حالة الموعد'),
                  SizedBox(height: 8.h),
                  DropdownButtonFormField<String>(
                    value: _selectedStatus,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: AppointmentModel.statusConfirmed,
                        child: Text('مؤكد'),
                      ),
                      DropdownMenuItem(
                        value: AppointmentModel.statusCompleted,
                        child: Text('مكتمل'),
                      ),
                      DropdownMenuItem(
                        value: AppointmentModel.statusCancelled,
                        child: Text('ملغي'),
                      ),
                      DropdownMenuItem(
                        value: AppointmentModel.statusNoShow,
                        child: Text('لم يحضر'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedStatus = value!;
                      });
                    },
                  ),

                  SizedBox(height: 20.h),

                  // Date Selection
                  _buildSectionTitle('تاريخ الموعد'),
                  SizedBox(height: 8.h),
                  InkWell(
                    onTap: _selectDate,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.primary),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.calendar_today, color: AppColors.primary),
                          SizedBox(width: 12.w),
                          Text(
                            _selectedDate == null
                                ? 'اختر التاريخ'
                                : '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: _selectedDate == null 
                                  ? AppColors.textSecondary 
                                  : AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 20.h),

                  // Time Slots Selection
                  if (_selectedDate != null) ...[
                    _buildSectionTitle('وقت الموعد'),
                    SizedBox(height: 8.h),
                    if (_isLoadingTimeSlots)
                      Center(
                        child: CircularProgressIndicator(color: AppColors.primary),
                      )
                    else if (_availableTimeSlots.isEmpty)
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(20.w),
                        decoration: BoxDecoration(
                          color: AppColors.error.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          'لا توجد أوقات متاحة في هذا التاريخ',
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    else
                      DropdownButtonFormField<TimeSlotModel>(
                        value: _selectedTimeSlot,
                        decoration: InputDecoration(
                          labelText: 'اختر الوقت',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        items: _availableTimeSlots.map((timeSlot) {
                          return DropdownMenuItem<TimeSlotModel>(
                            value: timeSlot,
                            child: Text(
                              '${timeSlot.startTime} - ${timeSlot.endTime}${timeSlot.employeeName != null ? ' (د. ${timeSlot.employeeName})' : ''}',
                              style: TextStyle(fontSize: 14.sp),
                            ),
                          );
                        }).toList(),
                        onChanged: (TimeSlotModel? value) {
                          setState(() {
                            _selectedTimeSlot = value;
                          });
                        },
                      ),

                    SizedBox(height: 20.h),
                  ],

                  // Payment Information
                  _buildSectionTitle('معلومات الدفع'),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          controller: _consultationFeeController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'سعر الكشف',
                            suffixText: 'د.ا',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Expanded(
                        child: TextFormField(
                          controller: _paidAmountController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'المبلغ المدفوع',
                            suffixText: 'د.ا',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 12.h),

                  // Remaining Amount Display
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: AppColors.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Text(
                      'المبلغ المتبقي: ${((double.tryParse(_consultationFeeController.text) ?? 0.0) - (double.tryParse(_paidAmountController.text) ?? 0.0)).toStringAsFixed(2)} د.ا',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.warning,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  SizedBox(height: 20.h),

                  // Notes
                  _buildSectionTitle('ملاحظات'),
                  SizedBox(height: 8.h),
                  TextFormField(
                    controller: _notesController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      hintText: 'أدخل ملاحظات إضافية...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),

                  SizedBox(height: 30.h),
                ],
              ),
            ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }
}
