import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../../core/models/appointment_model.dart';
import '../../../patients/data/repositories/patients_repository.dart';
import '../../../appointments/data/repositories/appointments_repository.dart';

class EnhancedPatientDetailsDialog extends StatefulWidget {
  final String patientId;
  final String patientName;
  final String? patientPhone;

  const EnhancedPatientDetailsDialog({
    super.key,
    required this.patientId,
    required this.patientName,
    this.patientPhone,
  });

  @override
  State<EnhancedPatientDetailsDialog> createState() => _EnhancedPatientDetailsDialogState();
}

class _EnhancedPatientDetailsDialogState extends State<EnhancedPatientDetailsDialog>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final PatientsRepository _patientsRepository = PatientsRepository();
  final AppointmentsRepository _appointmentsRepository = AppointmentsRepository();

  PatientModel? _patient;
  List<AppointmentModel> _patientAppointments = [];
  bool _isLoadingPatient = false;
  bool _isLoadingAppointments = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadPatientData();
    _loadPatientAppointments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadPatientData() async {
    setState(() {
      _isLoadingPatient = true;
    });

    try {
      final patient = await _patientsRepository.getPatientById(widget.patientId);
      setState(() {
        _patient = patient;
        _isLoadingPatient = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPatient = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات المريض: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _loadPatientAppointments() async {
    setState(() {
      _isLoadingAppointments = true;
    });

    try {
      final appointments = await _appointmentsRepository.getAppointmentsByPatient(widget.patientId);
      setState(() {
        _patientAppointments = appointments;
        _isLoadingAppointments = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingAppointments = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل مواعيد المريض: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20.r,
                    backgroundColor: AppColors.white,
                    child: Icon(
                      Icons.person,
                      color: AppColors.primary,
                      size: 24.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.patientName,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.white,
                          ),
                        ),
                        if (widget.patientPhone != null)
                          Text(
                            widget.patientPhone!,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.white.withValues(alpha: 0.8),
                            ),
                          ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: AppColors.white,
                      size: 24.sp,
                    ),
                  ),
                ],
              ),
            ),

            // Tabs
            Container(
              color: AppColors.white,
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.person_outline, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text('البيانات الشخصية'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.event_note, size: 20.sp),
                        SizedBox(width: 8.w),
                        Text('تاريخ المواعيد'),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Tab Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Personal Info Tab
                  _buildPersonalInfoTab(),
                  
                  // Appointments History Tab
                  _buildAppointmentsHistoryTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoTab() {
    if (_isLoadingPatient) {
      return Center(
        child: CircularProgressIndicator(color: AppColors.primary),
      );
    }

    if (_patient == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color: AppColors.error,
            ),
            SizedBox(height: 16.h),
            Text(
              'لم يتم العثور على بيانات المريض',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic Info
          _buildInfoSection(
            'المعلومات الأساسية',
            [
              _buildInfoRow('رقم المريض', _patient!.patientId ?? 'غير محدد'),
              _buildInfoRow('الاسم', _patient!.name),
              _buildInfoRow('البريد الإلكتروني', _patient!.email ?? 'غير محدد'),
              _buildInfoRow('رقم الهاتف', _patient!.phone ?? 'غير محدد'),
              _buildInfoRow('العمر', _patient!.age?.toString() ?? 'غير محدد'),
              _buildInfoRow('الجنس', _patient!.gender ?? 'غير محدد'),
              _buildInfoRow('نوع العضوية', _patient!.isPremium ? 'مميز' : 'عادي'),
            ],
          ),

          SizedBox(height: 20.h),

          // Medical Info
          if (_patient!.medicalConditions != null || 
              _patient!.allergies != null || 
              _patient!.medications != null) ...[
            _buildInfoSection(
              'المعلومات الطبية',
              [
                if (_patient!.medicalConditions != null)
                  _buildInfoRow('الحالات المرضية', _patient!.medicalConditions!),
                if (_patient!.allergies != null)
                  _buildInfoRow('الحساسية', _patient!.allergies!),
                if (_patient!.medications != null)
                  _buildInfoRow('الأدوية', _patient!.medications!),
                if (_patient!.supplements != null)
                  _buildInfoRow('المكملات الغذائية', _patient!.supplements!),
                if (_patient!.physicalActivity != null)
                  _buildInfoRow('النشاط البدني', _patient!.physicalActivity!),
              ],
            ),
            SizedBox(height: 20.h),
          ],

          // Treatment Types
          if (_patient!.treatmentTypes.isNotEmpty) ...[
            _buildInfoSection(
              'أنواع العلاج',
              _patient!.treatmentTypes.map((type) => 
                _buildInfoRow('نوع العلاج', type.displayName)
              ).toList(),
            ),
            SizedBox(height: 20.h),
          ],

          // Notes
          if (_patient!.notes != null && _patient!.notes!.isNotEmpty) ...[
            _buildInfoSection(
              'ملاحظات',
              [
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    _patient!.notes!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAppointmentsHistoryTab() {
    if (_isLoadingAppointments) {
      return Center(
        child: CircularProgressIndicator(color: AppColors.primary),
      );
    }

    if (_patientAppointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64.sp,
              color: AppColors.textSecondary,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد مواعيد سابقة',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _patientAppointments.length,
      itemBuilder: (context, index) {
        final appointment = _patientAppointments[index];
        return _buildAppointmentCard(appointment);
      },
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentCard(AppointmentModel appointment) {
    final statusColor = _getStatusColor(appointment.status);
    
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${appointment.appointmentDate.day}/${appointment.appointmentDate.month}/${appointment.appointmentDate.year}',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  appointment.statusDisplayName,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          if (appointment.appointmentTime != null) ...[
            SizedBox(height: 4.h),
            Text(
              'الوقت: ${appointment.appointmentTime}',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
          
          if (appointment.consultationFee > 0) ...[
            SizedBox(height: 4.h),
            Row(
              children: [
                Text(
                  'الرسوم: ${appointment.consultationFee.toStringAsFixed(2)} د.ا',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(width: 16.w),
                Text(
                  'المدفوع: ${appointment.paidAmount.toStringAsFixed(2)} د.ا',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.success,
                  ),
                ),
              ],
            ),
          ],
          
          if (appointment.notes != null && appointment.notes!.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: Text(
                appointment.notes!,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppColors.textSecondary,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'confirmed':
        return AppColors.primary;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      case 'no_show':
        return AppColors.warning;
      default:
        return AppColors.textSecondary;
    }
  }
}
