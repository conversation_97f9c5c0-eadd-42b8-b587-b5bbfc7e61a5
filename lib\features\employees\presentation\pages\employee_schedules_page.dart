import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/admin_model.dart';
import '../bloc/employees_bloc.dart';

class EmployeeSchedulesPage extends StatefulWidget {
  const EmployeeSchedulesPage({super.key});

  @override
  State<EmployeeSchedulesPage> createState() => _EmployeeSchedulesPageState();
}

class _EmployeeSchedulesPageState extends State<EmployeeSchedulesPage> {
  List<EmployeeModel> _employees = [];
  EmployeeModel? _selectedEmployee;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  void _loadEmployees() {
    context.read<EmployeesBloc>().add(const LoadEmployeesByType('specialist'));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(24),
            decoration: const BoxDecoration(
              color: AppColors.surface,
              border: Border(
                bottom: BorderSide(color: AppColors.border, width: 1),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.schedule,
                  size: 32,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 16),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'جداول عمل الموظفين',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'إدارة مواعيد وجداول عمل الأخصائيين',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (_selectedEmployee != null)
                  ElevatedButton.icon(
                    onPressed: () {
                      // TODO: Add schedule dialog
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('سيتم إضافة نافذة إضافة الجدول قريباً')),
                      );
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة جدول'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
              ],
            ),
          ),

          // Employee Selection
          Container(
            padding: const EdgeInsets.all(16),
            color: AppColors.surface,
            child: Row(
              children: [
                const Text(
                  'اختر الأخصائي:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: BlocConsumer<EmployeesBloc, EmployeesState>(
                    listener: (context, state) {
                      if (state is EmployeesLoaded) {
                        setState(() {
                          _employees = state.employees;
                          if (_employees.isNotEmpty && _selectedEmployee == null) {
                            _selectedEmployee = _employees.first;
                          }
                        });
                      }
                    },
                    builder: (context, state) {
                      if (state is EmployeesLoading) {
                        return const Center(child: CircularProgressIndicator());
                      }

                      if (_employees.isEmpty) {
                        return const Text(
                          'لا يوجد أخصائيين',
                          style: TextStyle(color: AppColors.textSecondary),
                        );
                      }

                      return DropdownButton<EmployeeModel>(
                        value: _selectedEmployee,
                        isExpanded: true,
                        items: _employees.map((employee) {
                          return DropdownMenuItem(
                            value: employee,
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 16,
                                  backgroundColor: employee.specialization?.color != null
                                      ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
                                      : AppColors.primary,
                                  child: Text(
                                    employee.name.isNotEmpty ? employee.name[0] : 'م',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        employee.name,
                                        style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      if (employee.specialization != null)
                                        Text(
                                          employee.specialization!.name,
                                          style: const TextStyle(
                                            fontSize: 12,
                                            color: AppColors.textSecondary,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (employee) {
                          setState(() {
                            _selectedEmployee = employee;
                          });
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),

          // Schedule Content
          Expanded(
            child: _selectedEmployee != null
                ? _buildScheduleContent(_selectedEmployee!)
                : const Center(
                    child: Text(
                      'اختر أخصائي لعرض جدوله',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleContent(EmployeeModel employee) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Employee Info Card
          Card(
            color: AppColors.surface,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: employee.specialization?.color != null
                        ? Color(int.parse(employee.specialization!.color.replaceFirst('#', '0xFF')))
                        : AppColors.primary,
                    child: Text(
                      employee.name.isNotEmpty ? employee.name[0] : 'م',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          employee.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        if (employee.specialization != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            employee.specialization!.name,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Icon(Icons.email, size: 16, color: AppColors.textSecondary),
                            const SizedBox(width: 4),
                            Text(
                              employee.email,
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Weekly Schedule
          const Text(
            'الجدول الأسبوعي',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // Days of Week
          Expanded(
            child: ListView.builder(
              itemCount: 7,
              itemBuilder: (context, index) {
                final dayNames = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
                return Card(
                  color: AppColors.surface,
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Container(
                          width: 80,
                          child: Text(
                            dayNames[index],
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            'لا يوجد جدول محدد', // TODO: Load actual schedule
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            // TODO: Add/Edit schedule for this day
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text('إضافة جدول ليوم ${dayNames[index]}')),
                            );
                          },
                          icon: const Icon(Icons.add, color: AppColors.primary),
                          tooltip: 'إضافة جدول',
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
