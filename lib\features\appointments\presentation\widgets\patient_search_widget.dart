import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/models/patient_model.dart';
import '../../../patients/data/repositories/patients_repository.dart';

class PatientSearchWidget extends StatefulWidget {
  final Function(PatientModel) onPatientSelected;
  final PatientModel? selectedPatient;

  const PatientSearchWidget({
    super.key,
    required this.onPatientSelected,
    this.selectedPatient,
  });

  @override
  State<PatientSearchWidget> createState() => _PatientSearchWidgetState();
}

class _PatientSearchWidgetState extends State<PatientSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  final PatientsRepository _patientsRepository = PatientsRepository();
  List<PatientModel> _searchResults = [];
  bool _isSearching = false;
  bool _showResults = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _searchPatients(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _showResults = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _showResults = true;
    });

    try {
      final patients = await _patientsRepository.searchPatients(query);
      setState(() {
        _searchResults = patients;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _selectPatient(PatientModel patient) {
    setState(() {
      _showResults = false;
      _searchController.text = patient.name;
    });
    widget.onPatientSelected(patient);
  }

  void _clearSelection() {
    setState(() {
      _searchController.clear();
      _searchResults = [];
      _showResults = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search Field
        TextFormField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'ابحث بالاسم أو رقم الهاتف أو رقم المريض...',
            prefixIcon: Icon(Icons.search, color: AppColors.primary),
            suffixIcon: widget.selectedPatient != null
                ? IconButton(
                    icon: Icon(Icons.clear, color: AppColors.error),
                    onPressed: _clearSelection,
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(color: AppColors.primary),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12.r),
              borderSide: BorderSide(color: AppColors.primary, width: 2),
            ),
          ),
          onChanged: _searchPatients,
          readOnly: widget.selectedPatient != null,
        ),

        // Selected Patient Card
        if (widget.selectedPatient != null) ...[
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: AppColors.primary),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20.r,
                  backgroundColor: AppColors.primary,
                  child: Icon(
                    Icons.person,
                    color: AppColors.white,
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.selectedPatient!.name,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'رقم المريض: ${widget.selectedPatient!.patientId ?? widget.selectedPatient!.id}',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      if (widget.selectedPatient!.phone != null)
                        Text(
                          'الهاتف: ${widget.selectedPatient!.phone}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      if (widget.selectedPatient!.treatmentTypes.isNotEmpty)
                        Text(
                          'نوع العلاج: ${widget.selectedPatient!.treatmentTypes.map((t) => t.displayName).join(', ')}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
                Icon(
                  Icons.check_circle,
                  color: AppColors.success,
                  size: 24.sp,
                ),
              ],
            ),
          ),
        ],

        // Search Results
        if (_showResults && widget.selectedPatient == null) ...[
          SizedBox(height: 8.h),
          Container(
            constraints: BoxConstraints(maxHeight: 200.h),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _isSearching
                ? Center(
                    child: Padding(
                      padding: EdgeInsets.all(20.w),
                      child: CircularProgressIndicator(
                        color: AppColors.primary,
                      ),
                    ),
                  )
                : _searchResults.isEmpty
                    ? Center(
                        child: Padding(
                          padding: EdgeInsets.all(20.w),
                          child: Text(
                            'لا توجد نتائج',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ),
                      )
                    : ListView.separated(
                        shrinkWrap: true,
                        itemCount: _searchResults.length,
                        separatorBuilder: (context, index) => Divider(
                          height: 1,
                          color: AppColors.primary.withValues(alpha: 0.1),
                        ),
                        itemBuilder: (context, index) {
                          final patient = _searchResults[index];
                          return ListTile(
                            leading: CircleAvatar(
                              radius: 16.r,
                              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                              child: Icon(
                                Icons.person,
                                color: AppColors.primary,
                                size: 16.sp,
                              ),
                            ),
                            title: Text(
                              patient.name,
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'رقم المريض: ${patient.patientId ?? patient.id}',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                                if (patient.phone != null)
                                  Text(
                                    'الهاتف: ${patient.phone}',
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                if (patient.treatmentTypes.isNotEmpty)
                                  Text(
                                    'نوع العلاج: ${patient.treatmentTypes.map((t) => t.displayName).join(', ')}',
                                    style: TextStyle(
                                      fontSize: 11.sp,
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                              ],
                            ),
                            onTap: () => _selectPatient(patient),
                          );
                        },
                      ),
          ),
        ],
      ],
    );
  }
}
